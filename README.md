# Flow API Python Library

A Python library for interacting with the available Flow AI models through a unified API. This library is intended for internal use only.

Important: The available models and capabilities are subject to change and differ by tenant.

## Installation

```bash
# For local development
pip install -e .
```

```bash
# For production
pip install git+ssh://*****************/ciandt_it/pyflow_api.git
```

## Configuration

Duplicate the `.env.example` file and rename it to `.env`. Then, fill in the required values.

```bash
FLOW_API_CLIENT_ID=your_client_id_here
FLOW_API_CLIENT_SECRET=your_client_secret_here
FLOW_API_APP_TO_ACCESS=your_app_to_access_here
FLOW_API_TENANT=your_flow_tenant_here

# Logging configuration
FLOW_API_LOG_FILE_PATH=/var/log/flow_api/log_file.log  # Set path if you want logs in a file
FLOW_API_CONSOLE_LOG_LEVEL=OFF  # Set to desired level (ALL, INFO, WARNING, ERROR, OFF)
FLOW_API_FILE_LOG_LEVEL=ALL  # Set to desired level (ALL, INFO, WARNING, ERROR, OFF)
```

## Quick Start

```python
from flow_api import FlowAPIClient

# Initialize client
client = FlowAPIClient()

# Simple text generation
response = client.get_answer(
    user_prompt="What is the capital of Brazil?"
)
print(response)
```

## Documentation

For detailed usage examples and all available functionality, see:

- **[USAGE.md](USAGE.md)** - Complete usage guide with examples for all capabilities
- **[examples/](examples/)** - Practical code examples for each functionality
- **[CHANGELOG.md](CHANGELOG.md)** - Model availability history and version changes

### Available Capabilities

- **Chat Conversation** - Text generation with system prompts and streaming
- **Image Recognition** - Analyze images with vision-capable models
- **Image Generation** - Generate images from text prompts
- **Text Embedding** - Convert text to vector embeddings
- **Speech to Text** - Transcribe audio files to text

## Available Models

The following models are currently available (as of 2025-01-16):

| Model                          | Provider        | Capabilities                                            | Input Tokens |
| ------------------------------ | --------------- | ------------------------------------------------------- | ------------ |
| **gpt-4o**                     | Azure OpenAI    | Chat, Image Recognition, Streaming, System Instructions | 128,000      |
| **gpt-4o-mini**                | Azure OpenAI    | Chat, Image Recognition, Streaming, System Instructions | 128,000      |
| **gpt-4.1**                    | Azure OpenAI    | Chat, Image Recognition, Streaming, System Instructions | 1,000,000    |
| **o1**                         | Azure OpenAI    | Chat, Image Recognition, Streaming                      | 200,000      |
| **o1-mini**                    | Azure OpenAI    | Chat, Streaming                                         | 128,000      |
| **o3-mini**                    | Azure OpenAI    | Chat, Streaming, System Instructions                    | 200,000      |
| **text-embedding-ada-002**     | Azure OpenAI    | Text Embedding, Streaming                               | 8,192        |
| **text-embedding-3-small**     | Azure OpenAI    | Text Embedding, Streaming                               | 8,192        |
| **dall-e-3**                   | Azure OpenAI    | Image Generation                                        | 1,000        |
| **DeepSeek-R1**                | Azure Foundry   | Chat, Streaming                                         | 128,000      |
| **gemini-2.5-pro**             | Google Gemini   | Chat, Image Recognition, Streaming, System Instructions | 1,048,576    |
| **gemini-2.0-flash**           | Google Gemini   | Chat, Image Recognition, Streaming, System Instructions | 1,048,576    |
| **textembedding-gecko@003**    | Google Gemini   | Text Embedding, Streaming                               | 8,000        |
| **anthropic.claude-37-sonnet** | Amazon Bedrock  | Chat, Image Recognition, Streaming, System Instructions | 200,000      |
| **meta.llama3-70b-instruct**   | Amazon Bedrock  | Chat, Streaming, System Instructions                    | 8,000        |
| **amazon.nova-lite**           | Amazon Bedrock  | Chat, Image Recognition, Streaming                      | 300,000      |
| **amazon.nova-micro**          | Amazon Bedrock  | Chat, Streaming                                         | 128,000      |
| **amazon.nova-pro**            | Amazon Bedrock  | Chat, Image Recognition, Streaming                      | 300,000      |
| **amazon.titan-embed-text-v2** | Amazon Bedrock  | Text Embedding                                          | 8,191        |
| **whisper**                    | Azure AI Speech | Speech to Text                                          | -            |

> **Capabilities Legend**: Some models have capabilities automatically inferred from their names (e.g., dall-e-3 → Image Generation, whisper → Speech to Text).

> **Note**: Available models may vary by tenant. Use `client.list_models()` to see models available in your configuration.

## Features

- **Dynamic Model Loading**: Models fetched from API (according to tenant) and cached locally
- **Automatic Token Management**: Handles authentication and token refresh
- **Capability-Based Selection**: Choose models by what they can do
- **Multi-Modal Support**: Text, images, and speech processing
- **Type Safety**: Full Python type hints
- **Hexagonal Architecture**: Clean, extensible design

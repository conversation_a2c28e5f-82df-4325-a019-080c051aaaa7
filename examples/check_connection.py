#!/usr/bin/env python3
"""
Connection check example with flow_api.

This example shows how to use the check_connection() method to verify
the API connection before making requests, avoiding delays during actual calls.
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flow_api import FlowAPIClient

# Load environment variables
load_dotenv()

def example_1_basic_connection_check():
    """Example 1: Basic connection check before making requests, for better UX."""
    print("\n=== Example 1: Basic Connection Check ===\n")
    
    client = FlowAPIClient()
    
    # Check connection at application start
    print("🔧 Initializing application...")
    connection_result = client.check_connection()
    
    if connection_result['status'] == 'connected':
        print(f"✅ Initialization complete: {connection_result['message']}")
        
        # Show user that app is ready
        print("🎉 Application ready! You can now make requests without delays.\n")
    else:
        print(f"❌ Initialization failed: {connection_result['message']}")
        print("🔧 Please check your configuration and try again.")
        print("   When you try send a request, we will automatically check the connection.")

    # Simulate user interactions
    user_prompts = [
        "What is artificial intelligence?",
        "Explain quantum computing in simple terms",
        "What are the benefits of renewable energy?"
    ]
    
    for i, prompt in enumerate(user_prompts, 1):
        print(f"👤 User Question {i}: {prompt}")

        connection_result = client.check_connection()
        if connection_result['status'] != 'connected':
            print("   ⏳ Re-establishing connection (this may take a few seconds)...")
        else:
            print("⚡ Processing (fast response due to pre-verified connection)...")
        
        
        try:
            start_time = __import__('time').time()
            # If the initial connection was not made, this will trigger a new connection check
            # and fetch a new token if needed (and take some time)
            response = client.with_capability("chat-conversation").get_answer(
                user_prompt=prompt
            )
            end_time = __import__('time').time()
            
            print(f"🤖 Response: {response[:150]}...")
            print(f"⏱️  Response time: {end_time - start_time:.2f} seconds\n")
            
        except Exception as e:
            print(f"❌ Error: {e}\n")


def example_2_connection_check_with_retry():
    """Example 2: Connection check with retry logic."""
    print("\n=== Example 2: Connection Check with Retry ===\n")
    
    client = FlowAPIClient()
    
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        print(f"🔍 Checking connection (attempt {retry_count + 1}/{max_retries})...")
        connection_result = client.check_connection()
        
        if connection_result['status'] == 'connected':
            print(f"✅ {connection_result['message']}")
            
            # Connection successful, proceed with requests
            print("\n💬 Making multiple requests...")
            try:
                # First request
                response1 = client.with_capability("chat-conversation").get_answer(
                    user_prompt="What is 2+2?"
                )
                print(f"🤖 Response 1: {response1}")
                
                # Second request (should use cached token)
                response2 = client.get_answer(
                    user_prompt="What is the largest planet in our solar system?"
                )
                print(f"🤖 Response 2: {response2}")
                
            except Exception as e:
                print(f"❌ Request failed: {e}")
            break
        else:
            retry_count += 1
            print(f"❌ Connection failed: {connection_result['message']}")
            if retry_count < max_retries:
                print(f"🔄 Retrying in 2 seconds...")
                import time
                time.sleep(2)
            else:
                print("⚠️  Max retries reached. Unable to establish connection.")


def example_3_connection_status_monitoring():
    """Example 3: Monitor connection status during application lifecycle."""
    print("\n=== Example 3: Connection Status Monitoring ===\n")
    
    client = FlowAPIClient()
    
    def check_and_report_connection():
        """Helper function to check and report connection status."""
        result = client.check_connection()
        status_emoji = "✅" if result['status'] == 'connected' else "❌"
        print(f"{status_emoji} Connection Status: {result['message']}")
        
        if result['status'] == 'connected':
            print(f"   📋 Token source: {result['token_source']}")
        elif 'error' in result:
            print(f"   🔍 Error: {result['error']}")
        
        return result['status'] == 'connected'
    
    # Initial connection check
    print("🚀 Application startup - checking initial connection...")
    if check_and_report_connection():
        print("   ✅ Ready to process requests\n")
        
        # Simulate some application work
        print("💼 Processing user requests...")
        try:
            # Request 1
            print("   📝 Request 1: Simple question")
            answer1 = client.with_capability("chat-conversation").get_answer(
                user_prompt="Hello, how are you?"
            )
            print(f"   🤖 Answer: {answer1[:100]}...")
            
            # Periodic connection check (e.g., every few requests)
            print("\n🔍 Periodic connection check...")
            check_and_report_connection()
            
            # Request 2
            print("\n   📝 Request 2: Another question")
            answer2 = client.get_answer(
                user_prompt="What's the weather like?"
            )
            print(f"   🤖 Answer: {answer2[:100]}...")
            
        except Exception as e:
            print(f"   ❌ Request processing failed: {e}")
            print("   🔍 Re-checking connection...")
            check_and_report_connection()
    else:
        print("   ⚠️  Application cannot start due to connection issues")


if __name__ == "__main__":
    print("🔗 Flow API Connection Check Examples")
    print("=" * 50)
    
    try:
        example_1_basic_connection_check()
        example_2_connection_check_with_retry()
        example_3_connection_status_monitoring()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Examples interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
    
    print("\n🏁 Examples completed!")
